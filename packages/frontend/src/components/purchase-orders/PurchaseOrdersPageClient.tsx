'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/purchase-orders/data-table';
import { createPurchaseOrderColumns } from '@/components/purchase-orders/columns';
import { PurchaseOrderDetail } from '@/components/purchase-orders/purchase-order-detail';
import { PurchaseOrder, PurchaseOrderQueryParams } from '@/types/purchase-order';
import {
  usePurchaseOrders,
  usePurchaseOrdersInvalidate,

  useCancelPurchaseOrder,
  useSendPurchaseOrder,
  useDeletePurchaseOrder,
  useExportPurchaseOrders,
} from '@/hooks/usePurchaseOrders';
import { useSuppliers } from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { DEFAULT_PURCHASE_ORDER_PAGINATION } from '@/lib/constants/purchase-order';

interface PurchaseOrdersPageClientProps {
  initialQuery: PurchaseOrderQueryParams;
}

export function PurchaseOrdersPageClient({
  initialQuery,
}: PurchaseOrdersPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<PurchaseOrderQueryParams>(initialQuery);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [showDetail, setShowDetail] = useState(false);

  // Use TanStack Query for data fetching
  const { data, isLoading, error, refetch } = usePurchaseOrders(query);

  // Use invalidation hook for stats refresh
  const { invalidateAll } = usePurchaseOrdersInvalidate();

  // Use mutation hooks for purchase order operations
  const cancelPurchaseOrderMutation = useCancelPurchaseOrder();
  const sendPurchaseOrderMutation = useSendPurchaseOrder();
  const deletePurchaseOrderMutation = useDeletePurchaseOrder();
  const exportPurchaseOrdersMutation = useExportPurchaseOrders();

  // Get suppliers for filter options
  const { data: suppliersResponse } = useSuppliers({ 
    page: 1, 
    limit: 100, 
    sortBy: 'name', 
    sortOrder: 'asc' 
  });

  const supplierOptions = suppliersResponse?.data?.map(supplier => ({
    value: supplier.id,
    label: `${supplier.name} (${supplier.code})`,
  })) || [];

  // Filter state
  const [filters, setFilters] = useState<PurchaseOrderQueryParams>({
    status: query.status,
    supplierId: query.supplierId,
    orderDateFrom: query.orderDateFrom,
    orderDateTo: query.orderDateTo,
    expectedDeliveryFrom: query.expectedDeliveryFrom,
    expectedDeliveryTo: query.expectedDeliveryTo,
  });

  const handleQueryChange = useCallback((newQuery: PurchaseOrderQueryParams) => {
    setQuery(newQuery);
  }, []);

  const handleFilterChange = useCallback((key: keyof PurchaseOrderQueryParams, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Apply filters to query
    setQuery(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filtering
    }));
  }, [filters]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      status: undefined,
      supplierId: undefined,
      orderDateFrom: undefined,
      orderDateTo: undefined,
      expectedDeliveryFrom: undefined,
      expectedDeliveryTo: undefined,
    };
    setFilters(clearedFilters);
    setQuery(prev => ({
      ...prev,
      ...clearedFilters,
      page: 1,
    }));
  }, []);

  const handleViewPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrder(purchaseOrder);
    setShowDetail(true);
  }, []);

  const handleEditPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}/edit`);
  }, [router]);

  const handleCreatePurchaseOrder = useCallback(() => {
    router.push('/dashboard/purchase-orders/new');
  }, [router]);



  const handleCancelPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder, reason: string) => {
    try {
      await cancelPurchaseOrderMutation.mutateAsync({ 
        id: purchaseOrder.id, 
        reason 
      });
      invalidateAll();
      if (selectedPurchaseOrder?.id === purchaseOrder.id) {
        // Refresh the selected purchase order
        refetch();
      }
    } catch (error) {
      console.error('Failed to cancel purchase order:', error);
    }
  }, [cancelPurchaseOrderMutation, invalidateAll, selectedPurchaseOrder, refetch]);

  const handleSendPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await sendPurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrder?.id === purchaseOrder.id) {
        // Refresh the selected purchase order
        refetch();
      }
    } catch (error) {
      console.error('Failed to send purchase order:', error);
    }
  }, [sendPurchaseOrderMutation, invalidateAll, selectedPurchaseOrder, refetch]);

  const handleDeletePurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await deletePurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrder?.id === purchaseOrder.id) {
        setShowDetail(false);
        setSelectedPurchaseOrder(null);
      }
    } catch (error) {
      console.error('Failed to delete purchase order:', error);
    }
  }, [deletePurchaseOrderMutation, invalidateAll, selectedPurchaseOrder]);

  const handlePrintPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      // This would typically generate and download a PDF
      toast.info('Fitur cetak akan segera tersedia');
    } catch (error) {
      console.error('Failed to print purchase order:', error);
      toast.error('Gagal mencetak purchase order');
    }
  }, []);

  const handleExport = useCallback(async () => {
    try {
      await exportPurchaseOrdersMutation.mutateAsync({
        ...query,
        format: 'xlsx',
      });
    } catch (error) {
      console.error('Failed to export purchase orders:', error);
    }
  }, [exportPurchaseOrdersMutation, query]);

  const handleRefresh = useCallback(() => {
    refetch();
    invalidateAll();
  }, [refetch, invalidateAll]);

  const handleBackToList = useCallback(() => {
    setShowDetail(false);
    setSelectedPurchaseOrder(null);
  }, []);

  // Create columns with action handlers
  const columns = createPurchaseOrderColumns({
    onView: handleViewPurchaseOrder,
    onEdit: handleEditPurchaseOrder,
    onDelete: handleDeletePurchaseOrder,
    onCancel: (po) => handleCancelPurchaseOrder(po, 'Dibatalkan dari tabel'),
    onSend: handleSendPurchaseOrder,
    onPrint: handlePrintPurchaseOrder,
  });

  if (showDetail && selectedPurchaseOrder) {
    return (
      <PurchaseOrderDetail
        purchaseOrder={selectedPurchaseOrder}
        onEdit={() => handleEditPurchaseOrder(selectedPurchaseOrder)}
        onCancel={(reason) => handleCancelPurchaseOrder(selectedPurchaseOrder, reason)}
        onSend={() => handleSendPurchaseOrder(selectedPurchaseOrder)}
        onPrint={() => handlePrintPurchaseOrder(selectedPurchaseOrder)}
        onBack={handleBackToList}
        isLoading={
          cancelPurchaseOrderMutation.isPending ||
          sendPurchaseOrderMutation.isPending
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Purchase Orders</h1>
          <p className="text-muted-foreground">
            Kelola purchase order dan pemesanan ke supplier
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={exportPurchaseOrdersMutation.isPending}
          >
            <Download className="h-4 w-4 mr-2" />
            {exportPurchaseOrdersMutation.isPending ? 'Mengekspor...' : 'Ekspor'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleCreatePurchaseOrder}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Purchase Order
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full">
            <DataTable
              columns={columns}
              data={data?.data || []}
              meta={data?.meta || { 
                total: 0, 
                page: 1, 
                limit: 10, 
                totalPages: 0, 
                hasNextPage: false, 
                hasPreviousPage: false 
              }}
              query={query}
              onQueryChange={handleQueryChange}
              loading={isLoading}
              searchPlaceholder="Cari purchase order..."
              onRowClick={handleViewPurchaseOrder}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              filterOptions={{
                suppliers: supplierOptions,
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
