// Purchase Order Types
export enum PurchaseOrderStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  ORDERED = 'ORDERED',
  PARTIALLY_RECEIVED = 'PARTIALLY_RECEIVED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum PurchaseOrderItemStatus {
  PENDING = 'PENDING',
  ORDERED = 'ORDERED',
  PARTIALLY_RECEIVED = 'PARTIALLY_RECEIVED',
  FULLY_RECEIVED = 'FULLY_RECEIVED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  CASH = 'CASH',
  TRANSFER = 'TRANSFER',
  CREDIT = 'CREDIT',
  GIRO = 'GIRO',
}

export interface PurchaseOrderItem {
  id: string;
  purchaseOrderId: string;
  productId: string;
  unitId: string;
  status: PurchaseOrderItemStatus;
  quantityOrdered: number;
  quantityReceived: number;
  unitPrice: number;
  totalPrice: number;
  discountType?: string;
  discountValue?: number;
  discountAmount: number;
  expectedDelivery?: string;
  actualDelivery?: string;
  qualitySpecs?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;

  // Relations
  product: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
}

export interface PurchaseOrder {
  id: string;
  orderNumber: string;
  supplierId: string;
  status: PurchaseOrderStatus;
  orderDate: string;
  expectedDelivery?: string;
  actualDelivery?: string;
  subtotal: number;
  discountType?: string;
  discountValue?: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  paymentStatus: string;

  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;

  // Relations
  supplier: {
    id: string;
    code: string;
    name: string;
    type: string;
    city?: string;
    phone?: string;
    email?: string;
  };

  createdByUser?: {
    id: string;
    name: string;
    email: string;
  };
  updatedByUser?: {
    id: string;
    name: string;
    email: string;
  };
  items: PurchaseOrderItem[];
}

export interface PurchaseOrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  status?: PurchaseOrderStatus;
  orderDateFrom?: string;
  orderDateTo?: string;
  expectedDeliveryFrom?: string;
  expectedDeliveryTo?: string;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PurchaseOrderListResponse {
  data: PurchaseOrder[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// API Response type (matches backend getStats() return)
export interface PurchaseOrderStatsResponse {
  totalOrders: number;
  statusStats: Record<string, number>;
  totalValue: number;
  pendingValue: number;
  activeValue: number;
  completedValue: number;
  valueStats: Record<string, number>;
  recentOrders: PurchaseOrder[];
  supplierStats: SupplierStats[];
  processingTimeAnalytics: ProcessingTimeAnalytics;
  volumeTrends: VolumeTrend[];
  period: string;
}

export interface SupplierStats {
  supplierId: string;
  supplierName: string;
  orderCount: number;
  totalValue: number;
}

export interface ProcessingTimeAnalytics {
  averageProcessingTime: number;
  totalOrders: number;
  efficiencyMetrics: {
    fastProcessing: number;
    slowProcessing: number;
    averageProcessing: number;
    fastPercentage: number;
    slowPercentage: number;
  };
}

export interface VolumeTrend {
  date: Date;
  count: number;
}

// Create Purchase Order DTOs
export interface CreatePurchaseOrderItemDto {
  productId: string;
  unitId: string;
  quantityOrdered: number;
  unitPrice: number;
  discountType?: string;
  discountValue?: number;
  expectedDelivery?: string;
  qualitySpecs?: string;
  notes?: string;
  updateSellingPrice?: boolean;
  newSellingPrice?: number;
}

export interface CreatePurchaseOrderDto {
  supplierId: string;
  orderDate?: string;
  expectedDelivery?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  autoCalculateTax?: boolean;
  taxInclusive?: boolean;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items: CreatePurchaseOrderItemDto[];
}

export interface UpdatePurchaseOrderDto {
  supplierId?: string;
  orderDate?: string;
  expectedDelivery?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  autoCalculateTax?: boolean;
  taxInclusive?: boolean;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items?: CreatePurchaseOrderItemDto[];
  // Additional fields from backend UpdatePurchaseOrderDto
  status?: PurchaseOrderStatus;
  actualDelivery?: string;
  paymentStatus?: string;

}



export interface PurchaseOrderStatusUpdateDto {
  status: PurchaseOrderStatus;
  notes?: string;
}

// Form types for frontend
export interface PurchaseOrderFormItem {
  productId: string;
  unitId: string;
  quantityOrdered: number;
  unitPrice: number;
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT';
  discountValue?: number;
  expectedDelivery?: string;
  qualitySpecs?: string;
  notes?: string;

  // Computed fields for display
  totalPrice?: number;
  discountAmount?: number;
  finalPrice?: number;

  // Product info for display
  product?: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
}

export interface PurchaseOrderFormData {
  supplierId: string;
  orderDate: string;
  expectedDelivery?: string;
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT';
  discountValue?: number;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items: PurchaseOrderFormItem[];

  // Computed totals
  subtotal?: number;
  discountAmount?: number;
  taxAmount?: number;
  totalAmount?: number;
}
